<div class="meeting-detail-page">
  <VanLoading v-if="loading" class="loading-container" />
  
  <div v-else-if="meetingDetail" class="detail-content">
    <!-- 会议基本信息 -->
    <div class="info-section">
      <h2 class="meeting-title">{{ meetingDetail.conferenceName }}</h2>
      
      <div class="info-grid">
        <div class="info-item">
          <VanIcon name="clock-o" class="info-icon" />
          <div class="info-content">
            <div class="info-label">开始时间</div>
            <div class="info-value">{{ meetingDetail.startTime }}</div>
          </div>
        </div>
        
        <div class="info-item">
          <VanIcon name="location-o" class="info-icon" />
          <div class="info-content">
            <div class="info-label">会议地点</div>
            <div class="info-value">{{ meetingDetail.location }}</div>
          </div>
        </div>
        
        <div class="info-item">
          <VanIcon name="user-o" class="info-icon" />
          <div class="info-content">
            <div class="info-label">主持人</div>
            <div class="info-value">{{ meetingDetail.host || '待定' }}</div>
          </div>
        </div>
        
        <div class="info-item">
          <VanIcon name="friends-o" class="info-icon" />
          <div class="info-content">
            <div class="info-label">参会人员</div>
            <div class="info-value">{{ meetingDetail.participants || '待定' }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 会议议题 -->
    <div class="agenda-section">
      <h3 class="section-title">
        <VanIcon name="notes-o" />
        会议议题
      </h3>
      <div class="agenda-list">
        <div
          v-for="(topic, index) in meetingDetail.agenda"
          :key="index"
          class="agenda-item"
        >
          <span class="agenda-number">{{ index + 1 }}</span>
          <span class="agenda-text">{{ topic }}</span>
        </div>
      </div>
    </div>
    
    <!-- 会议描述 -->
    <div v-if="meetingDetail.description" class="description-section">
      <h3 class="section-title">
        <VanIcon name="description" />
        会议描述
      </h3>
      <div class="description-content">
        {{ meetingDetail.description }}
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="action-section">
      <VanButton
        type="primary"
        size="large"
        round
        block
        @click="joinMeeting"
      >
        加入会议
      </VanButton>
      
      <VanButton
        type="default"
        size="large"
        round
        block
        class="action-btn"
        @click="exportCalendar"
      >
        导出到日历
      </VanButton>
    </div>
  </div>
  
  <!-- 错误状态 -->
  <VanEmpty
    v-else
    description="会议信息不存在"
    image="error"
  />
</div>

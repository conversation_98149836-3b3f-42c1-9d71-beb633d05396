<!-- 待开会议页面 -->
<template src='./html/index.html'></template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { meetingApi } from '@/api/meeting'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const meetingList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)

// 方法
const fetchMeetingList = async (isRefresh = false) => {
  if (loading.value && !isRefresh) return

  loading.value = true

  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟数据
    const mockData = {
      records: [
        {
          id: '1',
          conferenceName: '紧急技术评审',
          startTime: '2025-06-30 18:58:51',
          location: '成都市农业农村局1006会议室',
          agenda: '关于高标准农田建设的技术方案汇报；关于技术方案的头脑风暴讨论；关于建设周期的讨论',
          description: '针对高标准农田建设项目进行技术评审'
        },
        {
          id: '2',
          conferenceName: '紧急技术评审',
          startTime: '2025-06-30 18:58:51',
          location: '成都市农业农村局1006会议室成都市农业农村局1006会议室',
          agenda: '关于高标准农田建设的技术方案汇报；关于技术方案的头脑风暴讨论；关于建设周期的讨论',
          description: '针对高标准农田建设项目进行技术评审'
        }
      ],
      total: 2
    }

    // 如果有搜索关键词，过滤数据
    let filteredRecords = mockData.records
    if (searchKeyword.value) {
      filteredRecords = mockData.records.filter(meeting =>
        meeting.conferenceName.includes(searchKeyword.value)
      )
    }

    // 处理会议数据，解析议题
    const processedMeetings = filteredRecords.map(meeting => ({
      ...meeting,
      agenda: parseAgenda(meeting.agenda || ''),
      startTime: formatDateTime(meeting.startTime),
      location: meeting.location || '会议地点待定'
    }))

    if (isRefresh) {
      meetingList.value = processedMeetings
      currentPage.value = 1
    } else {
      meetingList.value.push(...processedMeetings)
    }

    // 判断是否还有更多数据
    finished.value = true // 模拟数据只有一页

    if (!isRefresh) {
      currentPage.value++
    }
  } catch (error) {
    console.error('获取会议列表失败:', error)
    showToast('获取会议列表失败')
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 解析会议议题
const parseAgenda = (agendaText) => {
  if (!agendaText) return []
  
  // 根据图片中的格式，议题用分号分隔
  return agendaText.split(/[;；]/).filter(item => item.trim()).map(item => item.trim())
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateTime
  }
}

// 下拉刷新
const onRefresh = () => {
  finished.value = false
  fetchMeetingList(true)
}

// 加载更多
const onLoad = () => {
  if (!finished.value) {
    fetchMeetingList()
  }
}

// 搜索
const onSearch = () => {
  currentPage.value = 1
  finished.value = false
  meetingList.value = []
  fetchMeetingList(true)
}

// 清空搜索
const onClear = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  finished.value = false
  meetingList.value = []
  fetchMeetingList(true)
}

// 查看详情
const viewDetail = (meeting) => {
  router.push({
    name: 'MeetingDetail',
    params: { id: meeting.id }
  })
}

// 生命周期
onMounted(() => {
  fetchMeetingList(true)
})
</script>

<style lang="scss" scoped>
.meeting-pending-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding-top: 46px; // 导航栏高度
}

.search-container {
  background: var(--van-background-2);
  padding: var(--van-padding-md);
  border-bottom: 1px solid var(--van-border-color);
}

.meeting-list {
  padding: var(--van-padding-md);
}

.meeting-item {
  background: var(--van-background-2);
  border-radius: var(--van-radius-lg);
  padding: var(--van-padding-lg);
  margin-bottom: var(--van-padding-md);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .meeting-header {
    margin-bottom: var(--van-padding-md);
    
    .meeting-title {
      font-size: var(--van-font-size-lg);
      font-weight: var(--van-font-bold);
      color: var(--van-text-color);
      margin: 0;
      line-height: var(--van-line-height-lg);
    }
  }

  .meeting-info {
    margin-bottom: var(--van-padding-lg);
    
    .info-row {
      display: flex;
      margin-bottom: var(--van-padding-sm);
      
      &.agenda {
        flex-direction: column;
        
        .agenda-content {
          margin-top: var(--van-padding-xs);
          
          p {
            margin: 0 0 var(--van-padding-xs) 0;
            color: var(--van-text-color-2);
            font-size: var(--van-font-size-md);
            line-height: var(--van-line-height-md);
          }
        }
      }
      
      .label {
        color: var(--van-text-color-2);
        font-size: var(--van-font-size-md);
        min-width: 80px;
        flex-shrink: 0;
      }
      
      .value {
        color: var(--van-text-color);
        font-size: var(--van-font-size-md);
        flex: 1;
      }
    }
  }

  .meeting-actions {
    text-align: center;
  }
}

// 响应式适配
@media (max-width: 320px) {
  .meeting-list {
    padding: var(--van-padding-sm);
  }
  
  .meeting-item {
    padding: var(--van-padding-md);
  }
}
</style>

<route lang="json5">
{
  name: 'MeetingPending',
  meta: {
    title: '待开会议'
  }
}
</route>

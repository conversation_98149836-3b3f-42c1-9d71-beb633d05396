<!-- 待开会议页面 -->
<template src='./html/index.html'></template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { meetingApi } from '@/api/meeting'
import { useUserStore } from '@/stores/modules/user'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const meetingList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)

// 方法
const fetchMeetingList = async (isRefresh = false) => {
  if (loading.value && !isRefresh) return

  loading.value = true

  try {
    const params = {
      current: isRefresh ? 1 : currentPage.value,
      size: pageSize.value,
      assignee: userStore.currentAssignee, // 从用户状态获取当前用户账号
      ...(searchKeyword.value && { conferenceName: searchKeyword.value })
    }

    const response = await meetingApi.getKeeperFromDataApp(params)

    // 检查响应结构
    if (response && response.success && response.data && response.data.records) {
      // 处理会议数据，适配新的数据结构
      const processedMeetings = response.data.records.map(meeting => ({
        ...meeting,
        id: meeting.fromId, // 使用 fromId 作为会议ID
        agenda: processAgendaList(meeting.meetingAgendaListVos || []),
        startTime: formatDateTime(meeting.startTime),
        location: meeting.confereAddress || '会议地点待定'
      }))

      if (isRefresh) {
        meetingList.value = processedMeetings
        currentPage.value = 1
      } else {
        meetingList.value.push(...processedMeetings)
      }

      // 判断是否还有更多数据
      const totalPages = response.data.pages || 0
      finished.value = currentPage.value >= totalPages

      if (!isRefresh) {
        currentPage.value++
      }
    } else {
      // 如果API调用失败，使用模拟数据进行开发测试
      console.warn('API调用失败，使用模拟数据')
      await loadMockData(isRefresh)
    }
  } catch (error) {
    console.error('获取会议列表失败:', error)
    // API调用失败时使用模拟数据
    await loadMockData(isRefresh)
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 模拟数据加载函数（用于开发测试）
const loadMockData = async (isRefresh = false) => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 800))

  const mockData = {
    records: [
      {
        fromId: '1',
        conferenceName: '紧急技术评审',
        startTime: '2025-06-30 18:58:51',
        confereAddress: '成都市农业农村局1006会议室',
        createTime: '2025-06-30 16:00:00',
        meetingAgendaListVos: [
          {
            id: 1,
            topicName: '关于高标准农田建设的技术方案汇报',
            topicContent: '详细介绍高标准农田建设的技术方案',
            agendaOrder: 1
          },
          {
            id: 2,
            topicName: '关于技术方案的头脑风暴讨论',
            topicContent: '针对技术方案进行深入讨论',
            agendaOrder: 2
          },
          {
            id: 3,
            topicName: '关于建设周期的讨论',
            topicContent: '确定项目建设时间安排',
            agendaOrder: 3
          }
        ]
      },
      {
        fromId: '2',
        conferenceName: '紧急技术评审',
        startTime: '2025-06-30 18:58:51',
        confereAddress: '成都市农业农村局1006会议室成都市农业农村局1006会议室',
        createTime: '2025-06-30 16:30:00',
        meetingAgendaListVos: [
          {
            id: 4,
            topicName: '关于高标准农田建设的技术方案汇报',
            topicContent: '技术方案详细说明',
            agendaOrder: 1
          },
          {
            id: 5,
            topicName: '关于技术方案的头脑风暴讨论',
            topicContent: '团队讨论环节',
            agendaOrder: 2
          },
          {
            id: 6,
            topicName: '关于建设周期的讨论',
            topicContent: '时间规划讨论',
            agendaOrder: 3
          }
        ]
      }
    ],
    total: 2,
    current: 1,
    pages: 1
  }

  // 如果有搜索关键词，过滤数据
  let filteredRecords = mockData.records
  if (searchKeyword.value) {
    filteredRecords = mockData.records.filter(meeting =>
      meeting.conferenceName.includes(searchKeyword.value)
    )
  }

  // 处理会议数据
  const processedMeetings = filteredRecords.map(meeting => ({
    ...meeting,
    id: meeting.fromId,
    agenda: processAgendaList(meeting.meetingAgendaListVos || []),
    startTime: formatDateTime(meeting.startTime),
    location: meeting.confereAddress || '会议地点待定'
  }))

  if (isRefresh) {
    meetingList.value = processedMeetings
    currentPage.value = 1
  } else {
    meetingList.value.push(...processedMeetings)
  }

  finished.value = true // 模拟数据只有一页
}

// 处理议题列表（新的数据结构）
const processAgendaList = (agendaList) => {
  if (!Array.isArray(agendaList) || agendaList.length === 0) return []

  // 按照 agendaOrder 排序，然后提取议题名称
  return agendaList
    .sort((a, b) => (a.agendaOrder || 0) - (b.agendaOrder || 0))
    .map(item => item.topicName || item.topicContent || '')
    .filter(item => item.trim())
}

// 解析会议议题（兼容旧格式）
const parseAgenda = (agendaText) => {
  if (!agendaText) return []

  // 根据图片中的格式，议题用分号分隔
  return agendaText.split(/[;；]/).filter(item => item.trim()).map(item => item.trim())
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateTime
  }
}

// 下拉刷新
const onRefresh = () => {
  finished.value = false
  fetchMeetingList(true)
}

// 加载更多
const onLoad = () => {
  if (!finished.value) {
    fetchMeetingList()
  }
}

// 搜索
const onSearch = () => {
  currentPage.value = 1
  finished.value = false
  meetingList.value = []
  fetchMeetingList(true)
}

// 清空搜索
const onClear = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  finished.value = false
  meetingList.value = []
  fetchMeetingList(true)
}

// 查看详情
const viewDetail = (meeting) => {
  router.push({
    name: 'MeetingDetail',
    params: { id: meeting.id }
  })
}

// 生命周期
onMounted(() => {
  fetchMeetingList(true)
})
</script>

<style lang="scss" scoped>
.meeting-pending-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding-top: 46px; // 导航栏高度
}

.search-container {
  background: var(--van-background-2);
  padding: var(--van-padding-md);
  border-bottom: 1px solid var(--van-border-color);
}

.meeting-list {
  padding: var(--van-padding-md);
}

.meeting-item {
  background: var(--van-background-2);
  border-radius: var(--van-radius-lg);
  padding: var(--van-padding-lg);
  margin-bottom: var(--van-padding-md);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .meeting-header {
    margin-bottom: var(--van-padding-md);
    
    .meeting-title {
      font-size: var(--van-font-size-lg);
      font-weight: var(--van-font-bold);
      color: var(--van-text-color);
      margin: 0;
      line-height: var(--van-line-height-lg);
    }
  }

  .meeting-info {
    margin-bottom: var(--van-padding-lg);
    
    .info-row {
      display: flex;
      margin-bottom: var(--van-padding-sm);
      
      &.agenda {
        flex-direction: column;
        
        .agenda-content {
          margin-top: var(--van-padding-xs);
          
          p {
            margin: 0 0 var(--van-padding-xs) 0;
            color: var(--van-text-color-2);
            font-size: var(--van-font-size-md);
            line-height: var(--van-line-height-md);
          }
        }
      }
      
      .label {
        color: var(--van-text-color-2);
        font-size: var(--van-font-size-md);
        min-width: 80px;
        flex-shrink: 0;
      }
      
      .value {
        color: var(--van-text-color);
        font-size: var(--van-font-size-md);
        flex: 1;
      }
    }
  }

  .meeting-actions {
    text-align: center;
  }
}

// 响应式适配
@media (max-width: 320px) {
  .meeting-list {
    padding: var(--van-padding-sm);
  }
  
  .meeting-item {
    padding: var(--van-padding-md);
  }
}
</style>

<route lang="json5">
{
  name: 'MeetingPending',
  meta: {
    title: '待开会议'
  }
}
</route>

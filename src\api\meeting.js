/**
 * 会议相关 API
 */
export const meetingApi = {
  /**
   * 获取待开会议列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} params.assignee - 当前用户账号
   * @param {string} [params.conferenceName] - 会议名称（可选）
   * @returns {Promise} 返回会议列表数据
   */
  getKeeperFromDataApp: (params) => {
    return window.$http.fetch('/conference/keeperFromDataApp', params)
  },

  /**
   * 搜索会议
   * @param {Object} params - 搜索参数
   * @param {string} params.conferenceName - 会议名称
   * @returns {Promise} 返回搜索结果
   */
  searchMeetings: (params) => {
    return window.$http.fetch('/conference/search', params)
  },

  /**
   * 获取会议详情
   * @param {Object} params - 查询参数
   * @param {string} params.id - 会议ID
   * @returns {Promise} 返回会议详情数据
   */
  getMeetingDetail: (params) => {
    return window.$http.fetch(`/conference/detail/${params.id}`)
  }
}

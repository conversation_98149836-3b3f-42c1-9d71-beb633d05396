<!-- 会议详情页面 -->
<template src='./html/index.html'></template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { meetingApi } from '@/api/meeting'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const meetingDetail = ref(null)

// 方法
const fetchMeetingDetail = async () => {
  loading.value = true

  try {
    const meetingId = route.params.id

    try {
      // 尝试调用真实API
      const response = await meetingApi.getMeetingDetail({ id: meetingId })

      if (response && response.success && response.data) {
        meetingDetail.value = {
          ...response.data,
          id: response.data.fromId,
          agenda: processAgendaList(response.data.meetingAgendaListVos || []),
          startTime: formatDateTime(response.data.startTime),
          location: response.data.confereAddress || '会议地点待定'
        }
        return
      }
    } catch (apiError) {
      console.warn('API调用失败，使用模拟数据:', apiError)
    }

    // API调用失败时使用模拟数据
    await new Promise(resolve => setTimeout(resolve, 800))

    const mockDetail = {
      fromId: meetingId,
      conferenceName: '紧急技术评审',
      startTime: '2025-06-30 18:58:51',
      confereAddress: '成都市农业农村局1006会议室',
      createTime: '2025-06-30 16:00:00',
      description: '针对高标准农田建设项目进行技术评审，讨论技术方案的可行性和实施计划。',
      host: '张主任',
      participants: '李工程师、王专家、陈经理等',
      meetingAgendaListVos: [
        {
          id: 1,
          topicName: '关于高标准农田建设的技术方案汇报',
          topicContent: '详细介绍高标准农田建设的技术方案，包括技术路线、实施方案等',
          agendaOrder: 1
        },
        {
          id: 2,
          topicName: '关于技术方案的头脑风暴讨论',
          topicContent: '针对技术方案进行深入讨论，收集各方意见和建议',
          agendaOrder: 2
        },
        {
          id: 3,
          topicName: '关于建设周期的讨论',
          topicContent: '确定项目建设时间安排，制定详细的时间计划',
          agendaOrder: 3
        }
      ]
    }

    meetingDetail.value = {
      ...mockDetail,
      id: mockDetail.fromId,
      agenda: processAgendaList(mockDetail.meetingAgendaListVos || []),
      startTime: formatDateTime(mockDetail.startTime),
      location: mockDetail.confereAddress || '会议地点待定'
    }
  } catch (error) {
    console.error('获取会议详情失败:', error)
    showToast('获取会议详情失败')
  } finally {
    loading.value = false
  }
}

// 处理议题列表（新的数据结构）
const processAgendaList = (agendaList) => {
  if (!Array.isArray(agendaList) || agendaList.length === 0) return []

  // 按照 agendaOrder 排序，然后提取议题名称
  return agendaList
    .sort((a, b) => (a.agendaOrder || 0) - (b.agendaOrder || 0))
    .map(item => item.topicName || item.topicContent || '')
    .filter(item => item.trim())
}

// 解析会议议题（兼容旧格式）
const parseAgenda = (agendaText) => {
  if (!agendaText) return []

  // 根据图片中的格式，议题用分号分隔
  return agendaText.split(/[;；]/).filter(item => item.trim()).map(item => item.trim())
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateTime
  }
}

// 加入会议
const joinMeeting = async () => {
  try {
    const result = await showConfirmDialog({
      title: '确认加入会议',
      message: `确定要加入会议"${meetingDetail.value.conferenceName}"吗？`
    })
    
    if (result === 'confirm') {
      // 这里可以添加加入会议的逻辑
      showToast('已加入会议')
    }
  } catch (error) {
    // 用户取消
  }
}

// 导出到日历
const exportCalendar = () => {
  try {
    const meeting = meetingDetail.value
    const startDate = new Date(meeting.startTime)
    const endDate = new Date(startDate.getTime() + 2 * 60 * 60 * 1000) // 假设会议时长2小时
    
    // 创建日历事件URL
    const calendarUrl = `data:text/calendar;charset=utf8,BEGIN:VCALENDAR
VERSION:2.0
BEGIN:VEVENT
DTSTART:${startDate.toISOString().replace(/[-:]/g, '').split('.')[0]}Z
DTEND:${endDate.toISOString().replace(/[-:]/g, '').split('.')[0]}Z
SUMMARY:${meeting.conferenceName}
DESCRIPTION:${meeting.agenda.join('; ')}
LOCATION:${meeting.location}
END:VEVENT
END:VCALENDAR`
    
    // 创建下载链接
    const link = document.createElement('a')
    link.href = calendarUrl
    link.download = `${meeting.conferenceName}.ics`
    link.click()
    
    showToast('日历事件已导出')
  } catch (error) {
    console.error('导出日历失败:', error)
    showToast('导出日历失败')
  }
}

// 生命周期
onMounted(() => {
  fetchMeetingDetail()
})
</script>

<style lang="scss" scoped>
.meeting-detail-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding-top: 46px; // 导航栏高度
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-content {
  padding: var(--van-padding-md);
}

.info-section {
  background: var(--van-background-2);
  border-radius: var(--van-radius-lg);
  padding: var(--van-padding-lg);
  margin-bottom: var(--van-padding-md);
  
  .meeting-title {
    font-size: 20px;
    font-weight: var(--van-font-bold);
    color: var(--van-text-color);
    margin: 0 0 var(--van-padding-lg) 0;
    text-align: center;
  }
  
  .info-grid {
    .info-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: var(--van-padding-md);
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-icon {
        color: var(--van-primary-color);
        font-size: 18px;
        margin-right: var(--van-padding-sm);
        margin-top: 2px;
        flex-shrink: 0;
      }
      
      .info-content {
        flex: 1;
        
        .info-label {
          font-size: var(--van-font-size-sm);
          color: var(--van-text-color-2);
          margin-bottom: 2px;
        }
        
        .info-value {
          font-size: var(--van-font-size-md);
          color: var(--van-text-color);
          line-height: var(--van-line-height-md);
        }
      }
    }
  }
}

.agenda-section,
.description-section {
  background: var(--van-background-2);
  border-radius: var(--van-radius-lg);
  padding: var(--van-padding-lg);
  margin-bottom: var(--van-padding-md);
  
  .section-title {
    display: flex;
    align-items: center;
    font-size: var(--van-font-size-lg);
    font-weight: var(--van-font-bold);
    color: var(--van-text-color);
    margin: 0 0 var(--van-padding-md) 0;
    
    .van-icon {
      color: var(--van-primary-color);
      margin-right: var(--van-padding-xs);
    }
  }
}

.agenda-list {
  .agenda-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--van-padding-sm);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .agenda-number {
      background: var(--van-primary-color);
      color: white;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--van-font-size-sm);
      margin-right: var(--van-padding-sm);
      flex-shrink: 0;
    }
    
    .agenda-text {
      flex: 1;
      font-size: var(--van-font-size-md);
      color: var(--van-text-color);
      line-height: var(--van-line-height-md);
    }
  }
}

.description-content {
  font-size: var(--van-font-size-md);
  color: var(--van-text-color);
  line-height: var(--van-line-height-md);
}

.action-section {
  padding: var(--van-padding-lg) 0;
  
  .action-btn {
    margin-top: var(--van-padding-md);
  }
}

// 响应式适配
@media (max-width: 320px) {
  .detail-content {
    padding: var(--van-padding-sm);
  }
  
  .info-section,
  .agenda-section,
  .description-section {
    padding: var(--van-padding-md);
  }
}
</style>

<route lang="json5">
{
  name: 'MeetingDetail',
  meta: {
    title: '会议详情'
  }
}
</route>

<div class="meeting-pending-page">
  <!-- 搜索栏 -->
  <div class="search-container">
    <VanSearch
      v-model="searchKeyword"
      placeholder="输入会议名称查询"
      @search="onSearch"
      @clear="onClear"
      shape="round"
      background="transparent"
    />
  </div>

  <!-- 会议列表 -->
  <div class="meeting-list">
    <VanPullRefresh v-model="refreshing" @refresh="onRefresh">
      <VanList
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div
          v-for="meeting in meetingList"
          :key="meeting.id"
          class="meeting-item"
        >
          <div class="meeting-header">
            <h3 class="meeting-title">{{ meeting.conferenceName }}</h3>
          </div>
          
          <div class="meeting-info">
            <div class="info-row">
              <span class="label">开始时间</span>
              <span class="value">{{ meeting.startTime }}</span>
            </div>
            
            <div class="info-row">
              <span class="label">会议地点</span>
              <span class="value">{{ meeting.location }}</span>
            </div>
            
            <div class="info-row agenda">
              <span class="label">会议议题</span>
              <div class="agenda-content">
                <p v-for="(topic, index) in meeting.agenda" :key="index">
                  {{ index + 1 }}. {{ topic }}
                </p>
              </div>
            </div>
          </div>
          
          <div class="meeting-actions">
            <VanButton
              type="success"
              size="large"
              round
              @click="viewDetail(meeting)"
            >
              查看详情
            </VanButton>
          </div>
        </div>

        <!-- 空状态 -->
        <VanEmpty
          v-if="!loading && meetingList.length === 0"
          description="暂无待开会议"
          image="search"
        />
      </VanList>
    </VanPullRefresh>
  </div>
</div>
